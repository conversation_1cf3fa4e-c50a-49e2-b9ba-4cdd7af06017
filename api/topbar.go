package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Models used by topbar API
type TopBarCategory struct {
	ID                uint   `json:"id"`
	KategoriProduk    string `json:"Kategori_Produk"`
	KodKategoriProduk string `json:"Kod_Kategori_Produk"`
	ImgPath           string `json:"img_path"`
}

type CustomPage struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug,omitempty"`
}

type Branch struct {
	Cawangan string `json:"cawangan"`
}

// Handler function for /top-bar
func TopBarHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var category []TopBarCategory
		var featuredCategory []TopBarCategory
		var customPage []CustomPage
		var branch []Branch

		// Category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_category_menu = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&category).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Featured category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_featured = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&featuredCategory).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Check slug column existence
		var colName string
		if err := tx.Raw(`SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ecomm_custom_page' AND COLUMN_NAME = 'slug'`).Scan(&colName).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		if colName != "" {
			if err := tx.Table("ecomm_custom_page").
				Where("status = ?", 1).
				Select("id, name, slug").
				Scan(&customPage).Error; err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
			}
		} else {
			if err := tx.Table("ecomm_custom_page").
				Where("status = ?", 1).
				Select("id, name").
				Scan(&customPage).Error; err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
			}
		}

		// Branch
		if err := tx.Table("56_maklumat_kedai").
			Where("status = ? AND ecomm_is_display = ?", 1, 1).
			Select("cawangan").
			Scan(&branch).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, map[string]interface{}{
			"category":          category,
			"featured_category": featuredCategory,
			"custom_page":       customPage,
			"branch":            branch,
		})
	}
}
